#!/usr/bin/env tsx

import { db } from '../src/lib/prisma';

/**
 * 迁移硬编码配置到配置表的脚本
 * 1. 完善现有数据库配置
 * 2. 添加缺失的数据库配置
 * 3. 确保所有配置都来自数据库而不是硬编码
 */

// 完整的数据库配置数据
const databaseConfigs = [
  {
    code: 'us_class',
    name: 'US Classification',
    category: 'Regulation',
    description: 'FDA医疗器械分类数据库，包含产品代码、器械类别等信息',
    accessLevel: 'free',
    tableName: 'us_class',
    modelName: 'uSClass',
    icon: '🇺🇸',
    sortOrder: 1,
  },
  {
    code: 'us_pmn',
    name: 'US Premarket Notification',
    category: 'Regulation',
    description: '美国FDA PMN(510k)医疗器械审批信息',
    accessLevel: 'free',
    tableName: 'us_pmn',
    modelName: 'uSPremarketNotification',
    icon: '🇺🇸',
    sortOrder: 2,
  },
  {
    code: 'deviceCNImported',
    name: '医疗器械模板',
    category: 'Regulation',
    description: '新建医疗器械数据库时的基础模板，包含完整的字段配置和表结构',
    accessLevel: 'free',
    tableName: 'device_cn_imported',
    modelName: 'DeviceCNImported',
    icon: '🏥',
    sortOrder: 3,
  },
  {
    code: 'freePat',
    name: '医药专利',
    category: '药物研发',
    description: '医药专利信息及原文下载',
    accessLevel: 'free',
    tableName: 'free_pat',
    modelName: 'FreePat',
    icon: '📋',
    sortOrder: 4,
  },
  {
    code: 'deviceCNEvaluation',
    name: '中国大陆审评',
    category: 'Regulation',
    description: '医疗器械审评进度跟踪、审评结论查询',
    accessLevel: 'premium',
    tableName: 'device_cn_evaluation',
    modelName: 'DeviceCNEvaluation',
    icon: '🔍',
    sortOrder: 5,
  },
  {
    code: 'deviceHK',
    name: '中国香港上市',
    category: 'Regulation',
    description: '中国香港已上市的医疗器械信息',
    accessLevel: 'premium',
    tableName: 'device_hk',
    modelName: 'DeviceHK',
    icon: '🇭🇰',
    sortOrder: 6,
  },
  {
    code: 'deviceUS',
    name: '美国上市',
    category: '全球器械',
    description: '美国FDA批准的医疗器械信息',
    accessLevel: 'premium',
    tableName: 'device_us',
    modelName: 'DeviceUS',
    icon: '🇺🇸',
    sortOrder: 7,
  },
  {
    code: 'deviceJP',
    name: '日本上市',
    category: '全球器械',
    description: '日本PMDA批准的医疗器械信息',
    accessLevel: 'premium',
    tableName: 'device_jp',
    modelName: 'DeviceJP',
    icon: '🇯🇵',
    sortOrder: 8,
  },
  {
    code: 'deviceUK',
    name: '英国上市',
    category: '全球器械',
    description: '英国MHRA批准的医疗器械信息',
    accessLevel: 'premium',
    tableName: 'device_uk',
    modelName: 'DeviceUK',
    icon: '🇬🇧',
    sortOrder: 9,
  },
];

async function migrateToConfigTables() {
  console.log('🚀 开始迁移硬编码配置到配置表...\n');

  try {
    // 1. 更新现有配置
    console.log('📝 1. 更新现有数据库配置...');
    
    for (const config of databaseConfigs) {
      const existing = await db.databaseConfig.findUnique({
        where: { code: config.code }
      });

      if (existing) {
        // 更新现有配置，添加缺失字段
        await db.databaseConfig.update({
          where: { code: config.code },
          data: {
            name: config.name,
            category: config.category,
            description: config.description,
            accessLevel: config.accessLevel,
            tableName: config.tableName,
            modelName: config.modelName,
            sortOrder: config.sortOrder,
            // 将图标信息存储在 exportConfig 中，因为没有专门的 icon 字段
            exportConfig: {
              icon: config.icon,
              ...(existing.exportConfig as any || {})
            }
          }
        });
        console.log(`   ✅ 更新配置: ${config.code}`);
      } else {
        // 创建新配置
        await db.databaseConfig.create({
          data: {
            code: config.code,
            name: config.name,
            category: config.category,
            description: config.description,
            accessLevel: config.accessLevel,
            tableName: config.tableName,
            modelName: config.modelName,
            sortOrder: config.sortOrder,
            exportConfig: {
              icon: config.icon
            }
          }
        });
        console.log(`   ✅ 创建配置: ${config.code}`);
      }
    }

    // 2. 检查字段配置完整性
    console.log('\n🔧 2. 检查字段配置完整性...');
    
    const activeDatabases = await db.databaseConfig.findMany({
      where: { isActive: true },
      select: { code: true, name: true }
    });

    for (const dbConfig of activeDatabases) {
      const fieldCount = await db.fieldConfig.count({
        where: {
          databaseCode: dbConfig.code,
          isActive: true
        }
      });

      console.log(`   📊 ${dbConfig.code}: ${fieldCount} 个字段配置`);
      
      if (fieldCount === 0) {
        console.log(`   ⚠️  ${dbConfig.code} 没有字段配置，需要手动添加`);
      }
    }

    // 3. 验证配置完整性
    console.log('\n✅ 3. 验证配置完整性...');
    
    const allConfigs = await db.databaseConfig.findMany({
      where: { isActive: true },
      select: {
        code: true,
        name: true,
        category: true,
        description: true,
        accessLevel: true,
        tableName: true,
        modelName: true,
        exportConfig: true,
        sortOrder: true
      },
      orderBy: { sortOrder: 'asc' }
    });

    console.log(`\n📋 找到 ${allConfigs.length} 个活跃的数据库配置:`);
    
    allConfigs.forEach(config => {
      const icon = (config.exportConfig as any)?.icon || '📊';
      console.log(`\n   ${icon} ${config.code}`);
      console.log(`      名称: ${config.name}`);
      console.log(`      分类: ${config.category}`);
      console.log(`      访问级别: ${config.accessLevel}`);
      console.log(`      表名: ${config.tableName || 'N/A'}`);
      console.log(`      模型名: ${config.modelName || 'N/A'}`);
      console.log(`      描述: ${config.description || 'N/A'}`);
    });

    console.log('\n🎉 配置迁移完成！');
    console.log('\n📝 下一步：');
    console.log('   1. 清理硬编码配置');
    console.log('   2. 更新配置获取逻辑');
    console.log('   3. 清除缓存并测试');

  } catch (error) {
    console.error('❌ 迁移失败:', error);
    throw error;
  }
}

// 执行迁移
if (require.main === module) {
  migrateToConfigTables()
    .then(() => {
      console.log('\n✨ 迁移完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 迁移失败:', error);
      process.exit(1);
    });
}

export { migrateToConfigTables };
